{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@boastitup/hooks": "workspace:*", "@boastitup/supabase": "workspace:*", "@boastitup/ui": "workspace:*", "@cloudinary/url-gen": "^1.19.0", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.0.0", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^3.6.0", "framer-motion": "^11.0.0", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.10.0", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.8", "sharp": "^0.33.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "video.js": "^8.6.0", "videojs-contrib-quality-levels": "^4.0.0", "videojs-hls-quality-selector": "^1.1.4", "zustand": "^4.5.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["instagram-gallery", "content-management", "video-player", "cloudinary", "nextjs", "react", "typescript", "tailwindcss", "social-media", "instagram-content", "reel-player", "content-organization", "image-gallery", "responsive-design", "instagram-marketing"], "devDependencies": {"@boastitup/hooks": "workspace:*", "@boastitup/ui": "workspace:*", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^14.0.0", "@storybook/addon-essentials": "^7.6.0", "@storybook/addon-interactions": "^7.6.0", "@storybook/addon-links": "^7.6.0", "@storybook/blocks": "^7.6.0", "@storybook/nextjs": "^7.6.0", "@storybook/react": "^7.6.0", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^14.2.0", "@testing-library/user-event": "^14.5.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.8", "@types/video.js": "^7.3.58", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "prettier": "^3.2.5", "storybook": "^7.6.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}