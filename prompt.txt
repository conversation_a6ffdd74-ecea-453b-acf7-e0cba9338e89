# Prompt for Building Trend Surge Detector Component

## **Task Overview**
Create a single, self-contained **TrendSurgeDetector.tsx** component for a Next.js application that displays live trend monitoring data from a Supabase database. This component should demonstrate trend surge detection functionality using the provided database schema and sample data.

## **Project Context**
- **App**: BOAST IT UP - Social media content management platform
- **Tech Stack**: Next.js 15, React 19, TypeScript, Tailwind CSS, Supabase
- **UI Library**: Radix UI components available via `@boastitup/ui`
- **Charts**: Chart.js available for visualizations
- **Data Files**: `data.json` (sample trends) and `definitions.txt` (database schema)

## **Database Schema (from definitions.txt)**
- **Table**: `unified_trends` with 26 columns
- **Key Fields**: trend_name, velocity_score, growth_percentage, race_position, category, trend_type, volume, primary_platform
- **Views**: `v_daily_top_trends`, `v_trend_race_data`
- **Function**: `get_filtered_trends(p_tenant_id, p_category, p_trend_type, p_start_date, p_end_date)`

## **Sample Data Context (from data.json)**
- 10 fitness/wellness trend records
- Tenant ID: `07582af5-5f8b-4fa8-b785-14e2b2252287`
- Velocity scores: 0.47 to 0.94
- Categories: Fitness, Wellness, Nutrition, Fitness Technology
- Platforms: instagram, tiktok, facebook, youtube

## **Component Requirements**

### **File Location**
Create: `apps/web/components/TrendSurgeDetector.tsx`

### **Core Functionality**
1. **Data Fetching**: Query Supabase using `get_filtered_trends` RPC function
2. **Trend Display**: Show trends in card/table format with key metrics
3. **Filtering**: Basic filters for category and trend_type
4. **Velocity Race**: Simple bar chart visualization showing race positions
5. **Real-time Updates**: Ability to refresh data

### **Component Structure**
```typescript
interface TrendSurgeDetectorProps {
  tenantId?: string; // Default to sample tenant ID
  className?: string;
}

export function TrendSurgeDetector({ tenantId, className }: TrendSurgeDetectorProps)
```

### **UI Sections**
1. **Header**: Title "Trend Surge Detector" with refresh button
2. **Filters**: Dropdowns for category and trend_type selection
3. **Metrics Summary**: Top 3 trends with key stats
4. **Trend List**: Scrollable list/grid of all trends
5. **Velocity Race**: Horizontal bar chart showing top 10 trends by race_position

### **Data Display Requirements**
For each trend show:
- Trend name (prominent)
- Velocity score with color coding
- Growth percentage with trend indicator
- Volume and 24h change
- Category and platform badges
- Race position (if applicable)

### **Styling Guidelines**
- Use Tailwind CSS classes
- Responsive design (mobile-first)
- Color coding for velocity categories:
  - Surging: Green (#10b981)
  - Rising: Blue (#3b82f6) 
  - Steady: Gray (#6b7280)
- Use existing `@boastitup/ui` components where possible

### **Technical Implementation**

#### **TypeScript Interfaces** (inline in component file):
```typescript
interface UnifiedTrend {
  id: string;
  tenant_id: string;
  brand_id: string;
  trend_name: string;
  trend_type: 'social' | 'web';
  category: string;
  subcategory: string;
  volume: number;
  growth_percentage: string;
  velocity_score: string;
  velocity_category: 'surging' | 'rising' | 'steady';
  race_position: number;
  primary_platform: string;
  trend_date: string;
  // ... other fields as needed
}

interface TrendFilters {
  category?: string;
  trendType?: string;
}
```

#### **Supabase Integration**:
```typescript
import { createClient } from '@boastitup/supabase/client';

// Use RPC function for filtering
const { data, error } = await supabase.rpc('get_filtered_trends', {
  p_tenant_id: tenantId,
  p_category: filters.category || null,
  p_trend_type: filters.trendType || null,
  p_start_date: null,
  p_end_date: null,
});
```

#### **State Management**:
```typescript
const [trends, setTrends] = useState<UnifiedTrend[]>([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);
const [filters, setFilters] = useState<TrendFilters>({});
```

### **Chart Implementation**
Use Chart.js for velocity race visualization:
- Horizontal bar chart
- X-axis: velocity_score
- Y-axis: trend_name (top 10 by race_position)
- Color bars by velocity_category
- Responsive and animated

### **Error Handling**
- Loading states with skeleton UI
- Error messages with retry functionality
- Graceful fallbacks for missing data
- Console logging for debugging

### **Default Configuration**
- Use sample tenant ID: `07582af5-5f8b-4fa8-b785-14e2b2252287`
- Default to showing current day trends
- Sort by velocity_score descending
- Limit initial display to top 20 trends

## **Acceptance Criteria**
1. ✅ Component renders without errors
2. ✅ Fetches real data from Supabase using provided schema
3. ✅ Displays trends with all required metrics
4. ✅ Filtering works for category and trend_type
5. ✅ Velocity race chart shows top trends visually
6. ✅ Responsive design works on mobile and desktop
7. ✅ Loading and error states handled properly
8. ✅ Uses sample data structure from data.json

## **Testing**
- Test with sample tenant ID
- Verify filtering combinations work
- Test responsive breakpoints
- Ensure chart renders correctly
- Test error scenarios (network failures)

## **Integration**
Component should be easily importable and usable in existing workspace pages:
```typescript
import { TrendSurgeDetector } from '@/components/TrendSurgeDetector';

// Usage in page
<TrendSurgeDetector tenantId="07582af5-5f8b-4fa8-b785-14e2b2252287" />
```

## **Deliverable**
Single file: `apps/web/components/TrendSurgeDetector.tsx` that is production-ready, well-typed, and demonstrates the complete trend surge detection functionality using the provided database schema and sample data.
